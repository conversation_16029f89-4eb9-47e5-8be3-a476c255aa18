{"name": "backend", "type": "module", "main": "src/index.ts", "outDir": "dist", "scripts": {"dev": "bun run --hot src/index.ts", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio"}, "dependencies": {"@paralleldrive/cuid2": "^2.2.2", "better-auth": "^1.2.7", "drizzle-orm": "^0.43.1", "hono": "^4.7.8", "pg": "^8.15.6", "postgres": "^3.4.5"}, "devDependencies": {"@types/bun": "latest", "@types/pg": "^8.11.14", "drizzle-kit": "^0.31.0", "tsx": "^4.19.4"}}