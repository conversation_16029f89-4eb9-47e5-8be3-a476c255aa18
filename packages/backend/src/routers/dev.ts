import { ClearDoseAuthedHono } from "../util";

export const devRouter = new ClearDoseAuthedHono();

export const otps: {
  email: string;
  otp: string;
  type: "sign-in" | "email-verification" | "forget-password";
}[] = [];

// devRouter.use("*", async (c, next) => {
//   const { MODE } = env<{ MODE: string }>(c);

//   if (MODE !== "development") {
//     throw new HTTPException(403, {
//       message: "This route is accessible only in development mode.",
//     });
//   }
//   return next();
// });

devRouter.get("/otp", async (c) => {
  return c.json(otps);
});
