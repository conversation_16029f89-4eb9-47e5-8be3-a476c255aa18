import { drizzleAdapter } from 'better-auth/adapters/drizzle';
import { betterAuth } from 'better-auth';
import { db } from './db/';
import {
  UserTable,
  SessionTable,
  AccountTable,
  VerificationTable,
} from './db/schema';
import { admin, emailOTP, openAPI } from 'better-auth/plugins';
import { permissions } from './permissions';
import { otps } from './routers/dev';

export const auth = betterAuth({
  appName: 'clear_dose',
  database: drizzleAdapter(db, {
    provider: 'pg',
    usePlural: true,
    schema: {
      users: UserTable,
      sessions: SessionTable,
      accounts: AccountTable,
      verifications: VerificationTable,
    },
  }),
  plugins: [
    emailOTP({
      async sendVerificationOTP({ email, otp, type }) {
        console.log(`${email}, ${otp}, ${type}`);
        otps.unshift({
          email,
          otp,
          type,
        });
      },
    }),
    admin({
      ac: permissions.ac,
      defaultRole: 'user',
      roles: {
        admin: permissions.admin,
        user: permissions.user,
      },
    }),
    openAPI(),
  ],
  user: {
    modelName: 'users',
    fields: {
      name: 'firstName',
    },
    additionalFields: {
      lastName: {
        type: 'string',
        required: true,
      },
    },
  },
  emailAndPassword: {
    enabled: true,
  },
});

export type Session = typeof auth.$Infer.Session;
