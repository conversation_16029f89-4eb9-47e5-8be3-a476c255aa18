import { Hono } from 'hono';
import { auth } from './auth';
import { checkAuth, currentUser } from './middlewares/user';

export type Variables = {
  user: typeof auth.$Infer.Session.user | null;
  session: typeof auth.$Infer.Session.session | null;
};

export type AuthedVariables = {
  user: typeof auth.$Infer.Session.user;
  session: typeof auth.$Infer.Session.session;
};

export class ClearDoseHono extends Hono<{
  Variables: Variables;
}> {
  constructor() {
    super({ strict: false });
    this.use('*', currentUser);
  }
}

export class ClearDoseAuthedHono extends Hono<{
  Variables: AuthedVariables;
}> {
  constructor() {
    super({ strict: false });
    this.use('*', checkAuth);
    this.use('*', currentUser);
  }
}
