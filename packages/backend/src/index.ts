import { Hono } from 'hono';
import { logger } from 'hono/logger';
import { HTTPException } from 'hono/http-exception';
import { auth } from './auth';
import { ClearDoseHono } from './util';

const app = new ClearDoseHono().basePath('/api');

app.use(logger());

app.onError((err, c) => {
  if (err instanceof HTTPException) {
    return c.json(
      {
        message: err.message,
        cause: err.cause,
      },
      err.status
    );
  } else {
    return c.json(
      {
        message: 'Internal error',
        cause: err,
      },
      500
    );
  }
});

app.get('/', (c) => {
  return c.text('Hello Hono!');
});

app.on(['POST', 'GET'], '/auth/**', (c) => auth.handler(c.req.raw));

app.get('/hello', (c) => {
  return c.json({ message: 'Hello Hono! From API' });
});

export default {
  port: +process.env.PORT!,
  fetch: app.fetch,
};
