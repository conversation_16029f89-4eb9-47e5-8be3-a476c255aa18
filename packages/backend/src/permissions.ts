import { createAccessControl } from "better-auth/plugins/access";
import {
  defaultStatements as adminDefaultStatements,
  adminAc,
} from "better-auth/plugins/admin/access";

const statement = {
  ...adminDefaultStatements,
  activity: ["create", "read", "update", "delete"],
  personality: ["create", "read", "update", "delete"],
  profession: ["create", "read", "update", "delete"],
} as const;

export const ac = createAccessControl(statement);

export const user = ac.newRole({
  activity: ["read"],
  personality: ["read"],
  profession: ["read"],
});

export const admin = ac.newRole({
  activity: ["create", "read", "update", "delete"],
  personality: ["create", "read", "update", "delete"],
  profession: ["create", "read", "update", "delete"],
  ...adminAc.statements,
});

export const permissions = {
  ac,
  user,
  admin,
};
