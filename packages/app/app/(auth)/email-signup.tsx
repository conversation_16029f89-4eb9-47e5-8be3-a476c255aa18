import React, { useState } from 'react';
import { StyleSheet, View, Text, SafeAreaView, ScrollView } from 'react-native';
import { router } from 'expo-router';

import { Button } from '@/components/ui/Button';
import { TextInput } from '@/components/ui/TextInput';
import { PasswordInput } from '@/components/ui/PasswordInput';

export default function EmailSignUp() {
  const [fullName, setFullName] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');

  // Password validation
  const hasMinLength = password.length >= 8;
  const hasUpperCase = /[A-Z]/.test(password);
  const hasLowerCase = /[a-z]/.test(password);
  const hasNumberOrSpecial = /[0-9!@#$%^&*(),.?":{}|<>]/.test(password);
  const passwordsMatch = password === confirmPassword && password !== '';

  const isFormValid =
    fullName.trim() !== '' &&
    email.trim() !== '' &&
    hasMinLength &&
    hasUpperCase &&
    hasLowerCase &&
    hasNumberOrSpecial &&
    passwordsMatch;

  const handleCreateAccount = () => {
    if (isFormValid) {
      // Navigate to OTP verification
      router.push('/otp-verification');
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Text style={styles.title}>Create your account</Text>
        <Text style={styles.subtitle}>Sign up to get your health in shape</Text>

        <View style={styles.form}>
          <TextInput
            placeholder="Full Name"
            value={fullName}
            onChangeText={setFullName}
            icon="person.fill"
            style={styles.input}
          />

          <TextInput
            placeholder="Email"
            value={email}
            onChangeText={setEmail}
            keyboardType="email-address"
            autoCapitalize="none"
            icon="envelope.fill"
            style={styles.input}
          />

          <PasswordInput
            value={password}
            onChangeText={setPassword}
            containerStyle={styles.input}
          />

          <PasswordInput
            value={confirmPassword}
            onChangeText={setConfirmPassword}
            placeholder="Confirm Password"
            containerStyle={styles.input}
          />

          <View style={styles.validationContainer}>
            <Text style={styles.validationTitle}>Password must contain:</Text>
            <View style={styles.validationItem}>
              <View style={[styles.dot, hasMinLength && styles.validDot]} />
              <Text style={styles.validationText}>Minimum 8 characters</Text>
            </View>
            <View style={styles.validationItem}>
              <View style={[styles.dot, hasUpperCase && styles.validDot]} />
              <Text style={styles.validationText}>
                At least 1 uppercase letter
              </Text>
            </View>
            <View style={styles.validationItem}>
              <View style={[styles.dot, hasLowerCase && styles.validDot]} />
              <Text style={styles.validationText}>
                At least 1 lowercase letter
              </Text>
            </View>
            <View style={styles.validationItem}>
              <View
                style={[styles.dot, hasNumberOrSpecial && styles.validDot]}
              />
              <Text style={styles.validationText}>
                Minimum 1 number or 1 special character
              </Text>
            </View>
            {confirmPassword.length > 0 && (
              <View style={styles.validationItem}>
                <View style={[styles.dot, passwordsMatch && styles.validDot]} />
                <Text style={styles.validationText}>Passwords match</Text>
              </View>
            )}
          </View>

          <Button
            title="Create Account"
            onPress={handleCreateAccount}
            disabled={!isFormValid}
            style={styles.button}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingTop: 40,
    paddingBottom: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 32,
  },
  form: {
    width: '100%',
  },
  input: {
    marginBottom: 16,
  },
  validationContainer: {
    marginTop: 8,
    marginBottom: 24,
  },
  validationTitle: {
    fontSize: 14,
    fontWeight: '600',
    color: '#333333',
    marginBottom: 8,
  },
  validationItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
  },
  dot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: '#E5E5E5',
    marginRight: 8,
  },
  validDot: {
    backgroundColor: '#00C896',
  },
  validationText: {
    fontSize: 14,
    color: '#666666',
  },
  button: {
    marginTop: 16,
  },
});
