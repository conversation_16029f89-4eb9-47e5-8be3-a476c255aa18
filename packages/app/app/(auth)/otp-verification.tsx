import React, { useState, useEffect, useRef } from 'react';
import { StyleSheet, View, Text, SafeAreaView } from 'react-native';
import { router } from 'expo-router';

import { Button } from '@/components/ui/Button';
import OTPInput from '@/components/ui/OTPInput';

export default function OtpVerification() {
  const [code, setCode] = useState<string[]>(Array(4).fill(''));
  const [timeLeft, setTimeLeft] = useState(150); // 2:30 in seconds
  const [isResending, setIsResending] = useState(false);
  const timerRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  useEffect(() => {
    if (timeLeft > 0) {
      timerRef.current = setTimeout(() => {
        setTimeLeft((prevTime) => prevTime - 1);
      }, 1000);
    }

    return () => {
      if (timerRef.current) {
        clearTimeout(timerRef.current);
      }
    };
  }, [timeLeft]);

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs
      .toString()
      .padStart(2, '0')}`;
  };

  const handleCodeFilled = (code: string) => {
    // In a real app, you would verify the OTP here
    router.replace('/(tabs)');
  };

  const handleResend = () => {
    setIsResending(true);
    // Simulate OTP resend
    setTimeout(() => {
      setTimeLeft(150);
      setIsResending(false);
      setCode(Array(4).fill(''));
    }, 1000);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Text style={styles.title}>Verification</Text>
        <Text style={styles.subtitle}>
          OTP has been <NAME_EMAIL>
        </Text>

        <View style={styles.otpContainer}>
          <OTPInput
            codeLength={4}
            onCodeFilled={handleCodeFilled}
            code={code}
            setCode={setCode}
          />
        </View>

        <View style={styles.timerContainer}>
          <Text style={styles.timerText}>
            Expires in {formatTime(timeLeft)}
          </Text>
        </View>

        <Button
          title="Verify"
          onPress={() => {
            if (code.every((digit) => digit !== '')) {
              handleCodeFilled(code.join(''));
            }
          }}
          disabled={code.includes('')}
          style={styles.button}
        />

        <Button
          title="Resend OTP"
          variant="outline"
          onPress={handleResend}
          disabled={timeLeft > 0 || isResending}
          loading={isResending}
          style={styles.resendButton}
        />
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontWeight: 'bold',
    color: '#333333',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    color: '#666666',
    marginBottom: 32,
  },
  otpContainer: {
    marginBottom: 24,
  },
  timerContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  timerText: {
    fontSize: 14,
    color: '#666666',
  },
  button: {
    marginBottom: 16,
  },
  resendButton: {
    marginBottom: 16,
  },
});
