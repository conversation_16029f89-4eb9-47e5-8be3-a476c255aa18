import React from 'react';
import { StyleSheet, View, Text, SafeAreaView } from 'react-native';
import { Image } from 'expo-image';
import { router } from 'expo-router';

import { Button } from '@/components/ui/Button';

export default function AuthScreen() {
  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.content}>
        <Image
          source={require('@/assets/images/ic_logo.svg')}
          style={styles.logo}
          contentFit="contain"
        />

        <Text style={styles.title}>
          Upgrade peptides, interpret findings, get set for tomorrow
        </Text>

        <Text style={styles.subtitle}>
          Register or sign in to control your peptides, track your progress, and
          gain access to AI meal and workout plans.
        </Text>

        <View style={styles.buttonContainer}>
          <Button
            title="Register"
            onPress={() => router.push('/signup-choice')}
            style={styles.button}
          />

          <Button
            title="Log In"
            variant="secondary"
            onPress={() => router.push('/login')}
            style={styles.button}
          />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 40,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666666',
    lineHeight: 24,
    marginBottom: 40,
  },
  buttonContainer: {
    width: '100%',
    gap: 16,
  },
  button: {
    marginBottom: 16,
  },
});
