import React, { useEffect } from 'react';
import { ActivityIndicator, StyleSheet } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';
import { Image } from 'expo-image';
import { ThemedText } from '@/components/ThemedText';
import { StatusBar } from 'expo-status-bar';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

export default function SplashScreen() {
  useEffect(() => {
    const checkOnboardingStatus = async () => {
      try {
        // Wait a bit to show the splash screen
        await new Promise((resolve) => setTimeout(resolve, 1500));

        // Check if onboarding has been completed
        const onboardingCompleted = await AsyncStorage.getItem(
          'onboardingCompleted'
        );

        if (onboardingCompleted === 'true') {
          // If onboarding is completed, go to auth screen
          router.replace('/(auth)');
        } else {
          // If onboarding is not completed, go to onboarding screen
          router.replace('/(onboarding)');
        }
      } catch (error) {
        console.error('Error checking onboarding status:', error);
        // Default to onboarding if there's an error
        router.replace('/(onboarding)');
      }
    };

    checkOnboardingStatus();
  }, []);

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="light" />
      <Image
        source={require('@/assets/images/ic_logo.svg')}
        style={styles.logo}
        contentFit="contain"
      />
      <ThemedText style={styles.appName}>Clear Dose</ThemedText>
      <ActivityIndicator size="large" color="#00C896" style={styles.spinner} />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#F5F4F7',
    alignItems: 'center',
    justifyContent: 'center',
  },
  logo: {
    width: 120,
    height: 120,
    marginBottom: 20,
  },
  appName: {
    color: '#00C896',
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 40,
  },
  spinner: {
    position: 'absolute',
    bottom: 40,
  },
});
