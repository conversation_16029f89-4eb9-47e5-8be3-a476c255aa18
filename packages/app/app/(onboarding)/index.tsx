import React, { useState } from 'react';
import {
  StyleSheet,
  View,
  Text,
  SafeAreaView,
  TouchableOpacity,
  Dimensions,
} from 'react-native';
import { Image } from 'expo-image';
import { router } from 'expo-router';
import AsyncStorage from '@react-native-async-storage/async-storage';

import { Button } from '@/components/ui/Button';
import { ProgressDots } from '@/components/ui/ProgressDots';

const { width } = Dimensions.get('window');

const onboardingData = [
  {
    id: 1,
    image: require('@/assets/images/il_onboarding_1.svg'),
    title: 'Clear Dose – Your Medication Management Solution!',
    subtitle:
      'Personalized peptide tracking, meal & workout plans, and AI-powered guidance.',
    buttonText: 'Get Started',
  },
  {
    id: 2,
    image: require('@/assets/images/il_onboarding_2.svg'),
    title: 'Reliable Med Reminders at Your Fingertips!',
    subtitle:
      'Stay on track with timely alerts and effortless medication management.',
    buttonText: 'Next',
  },
  {
    id: 3,
    image: require('@/assets/images/il_onboarding_3.png'),
    title: 'Effortless Medication Management Starts Now!',
    subtitle: 'Seamless tracking for better health management.',
    buttonText: 'Next',
  },
];

export default function OnboardingScreen() {
  const [currentStep, setCurrentStep] = useState(0);

  const handleNext = async () => {
    if (currentStep < onboardingData.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      // Mark onboarding as completed
      await AsyncStorage.setItem('onboardingCompleted', 'true');
      // Navigate to auth screen
      router.replace('/(auth)');
    }
  };

  const handleSkip = async () => {
    // Mark onboarding as completed
    await AsyncStorage.setItem('onboardingCompleted', 'true');
    // Navigate to auth screen
    router.replace('/(auth)');
  };

  const currentData = onboardingData[currentStep];

  return (
    <SafeAreaView style={styles.container}>
      {currentStep > 0 && (
        <TouchableOpacity style={styles.skipButton} onPress={handleSkip}>
          <Text style={styles.skipText}>Skip</Text>
        </TouchableOpacity>
      )}

      <View style={styles.content}>
        <Image
          source={currentData.image}
          style={styles.image}
          contentFit="contain"
        />

        <View style={styles.textContainer}>
          <Text style={styles.title}>{currentData.title}</Text>
          <Text style={styles.subtitle}>{currentData.subtitle}</Text>
        </View>

        <ProgressDots total={onboardingData.length} current={currentStep} />

        <View style={styles.buttonContainer}>
          <Button title={currentData.buttonText} onPress={handleNext} />
        </View>
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#FFFFFF',
  },
  skipButton: {
    position: 'absolute',
    top: 20,
    right: 20,
    zIndex: 10,
  },
  skipText: {
    fontSize: 16,
    color: '#00C896',
    fontWeight: '600',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 24,
  },
  image: {
    width: width * 0.8,
    height: width * 0.8,
    marginBottom: 40,
  },
  textContainer: {
    alignItems: 'center',
    marginBottom: 30,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 16,
    color: '#333333',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    color: '#666666',
    lineHeight: 24,
  },
  buttonContainer: {
    width: '100%',
    marginTop: 20,
  },
});
