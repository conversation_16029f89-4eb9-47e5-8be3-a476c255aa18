// Fallback for using MaterialIcons on Android and web.

import React from 'react';
import { StyleSheet, Text, TextStyle } from 'react-native';

type Props = {
  name: string;
  size?: number;
  color?: string;
  style?: TextStyle;
};

// This is a placeholder for SF Symbols or another icon system
// In a real app, you would use a proper icon library like @expo/vector-icons
export function IconSymbol({ name, size = 24, color = '#000', style }: Props) {
  // This is just a placeholder implementation
  return (
    <Text style={[{ fontSize: size, color }, style]}>
      {name.includes('eye')
        ? '👁️'
        : name.includes('lock')
        ? '🔒'
        : name.includes('person')
        ? '👤'
        : name.includes('envelope')
        ? '✉️'
        : '●'}
    </Text>
  );
}
