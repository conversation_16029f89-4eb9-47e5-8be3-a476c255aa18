import type React from 'react';
import { useRef } from 'react';
import { Keyboard, StyleSheet, TextInput, View } from 'react-native';
import { ThemedText } from '../ThemedText';

interface OTPInputProps {
  codeLength?: number;
  onCodeFilled?: (code: string) => void;
  code: string[];
  setCode: React.Dispatch<React.SetStateAction<string[]>>;
}

export default function OTPInput({
  codeLength = 4,
  onCodeFilled = () => {},
  code,
  setCode,
}: OTPInputProps) {
  const inputRefs = useRef<(TextInput | null)[]>([]);

  const handleTextChange = (text: string, index: number) => {
    if (text.length > 1) {
      handlePastedText(text);
      return;
    }

    const newCode = [...code];
    newCode[index] = text;
    setCode(newCode);

    if (text.length === 1 && index < codeLength - 1) {
      inputRefs.current[index + 1]?.focus();
    }

    if (newCode.every((digit) => digit !== '') && !newCode.includes('')) {
      onCodeFilled(newCode.join(''));
      Keyboard.dismiss();
    }
  };

  const handleKeyPress = (e: any, index: number) => {
    if (e.nativeEvent.key === 'Backspace' && index > 0 && code[index] === '') {
      inputRefs.current[index - 1]?.focus();
    }
  };

  const handlePastedText = (text: string) => {
    const cleanedText = text.replace(/[^0-9]/g, '').slice(0, codeLength);
    if (cleanedText.length === 0) return;

    const newCode = [...code];
    for (let i = 0; i < cleanedText.length; i++) {
      newCode[i] = cleanedText[i];
    }
    setCode(newCode);

    const nextEmptyIndex = newCode.findIndex((digit) => digit === '');
    if (nextEmptyIndex !== -1) {
      inputRefs.current[nextEmptyIndex]?.focus();
    } else {
      inputRefs.current[codeLength - 1]?.focus();
    }

    if (newCode.every((digit) => digit !== '') && !newCode.includes('')) {
      onCodeFilled(newCode.join(''));
      Keyboard.dismiss();
    }
  };

  const renderInputs = () => {
    return Array(codeLength)
      .fill(0)
      .map((_, index) => {
        const hasValue = code[index] !== '';

        return (
          <View
            key={index}
            style={[
              styles.inputContainer,
              hasValue ? styles.inputContainerFilled : styles.inputContainerEmpty,
            ]}
          >
            <TextInput
              ref={(ref) => {
                inputRefs.current[index] = ref;
              }}
              style={[
                styles.textInput,
                hasValue ? styles.textInputFilled : styles.textInputEmpty,
              ]}
              keyboardType="number-pad"
              maxLength={1}
              value={code[index]}
              onChangeText={(text) => handleTextChange(text, index)}
              onKeyPress={(e) => handleKeyPress(e, index)}
              selectionColor="#6B46C1"
              autoFocus={index === 0}
            />
          </View>
        );
      });
  };

  return (
    <View>
      <ThemedText type="title">Enter The Code</ThemedText>
      <View style={styles.container}>{renderInputs()}</View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginTop: 16,
    paddingHorizontal: 16,
  },
  inputContainer: {
    width: 64,
    height: 64,
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 12,
    marginHorizontal: 4,
  },
  inputContainerEmpty: {
    backgroundColor: '#F9FAFB',
    borderWidth: 2,
    borderColor: '#E5E7EB',
  },
  inputContainerFilled: {
    backgroundColor: '#FFFFFF',
    borderWidth: 2,
    borderColor: '#6B46C1',
  },
  textInput: {
    width: '100%',
    height: '100%',
    textAlign: 'center',
    fontSize: 24,
    fontWeight: '600',
    color: '#1F2937',
  },
  textInputEmpty: {
    color: '#9CA3AF',
  },
  textInputFilled: {
    color: '#6B46C1',
  },
});
