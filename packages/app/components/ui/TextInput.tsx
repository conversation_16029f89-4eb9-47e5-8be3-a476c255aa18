import React from 'react';
import {
  StyleSheet,
  TextInput as RNTextInput,
  View,
  TextInputProps,
  ViewStyle,
} from 'react-native';
import { IconSymbol } from './IconSymbol';

type Props = TextInputProps & {
  icon?: string;
  error?: string;
  containerStyle?: ViewStyle;
  right?: React.ReactNode;
};

export function TextInput({
  icon,
  error,
  containerStyle,
  style,
  placeholderTextColor = '#A0A0A0',
  right,
  ...rest
}: Props) {
  return (
    <View
      style={[
        styles.container,
        containerStyle,
        error ? styles.errorContainer : null,
      ]}
    >
      {icon && (
        <IconSymbol
          name={icon as any}
          size={20}
          color={error ? '#FF3B30' : '#A0A0A0'}
          style={styles.icon}
        />
      )}
      <RNTextInput
        style={[styles.input, style]}
        placeholderTextColor={placeholderTextColor}
        {...rest}
      />
      {right}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#E5E5E5',
    borderRadius: 12,
    paddingHorizontal: 16,
    height: 56,
    backgroundColor: '#FFFFFF',
  },
  errorContainer: {
    borderColor: '#FF3B30',
  },
  icon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: '#333333',
  },
});
